# Phase 1: Foundation & Infrastructure

**Duration**: 2-3 weeks | **Priority**: Critical

## Overview
This phase establishes the foundational architecture for the chat application, including project setup, user authentication, and basic infrastructure components.

## Prerequisites
- Node.js 18+ installed
- Python 3.9+ installed
- PostgreSQL 13+ installed
- Redis 6+ installed
- Git version control

## Architecture Setup

### Project Structure
```
chatapp/
├── backend/                 # Django + DRF
│   ├── chatapp/
│   ├── apps/
│   │   ├── authentication/
│   │   ├── users/
│   │   └── core/
│   ├── requirements.txt
│   └── manage.py
├── frontend/               # React.js
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── types/
│   ├── package.json
│   └── public/
├── socket-server/          # Node.js + Socket.io
│   ├── src/
│   │   ├── handlers/
│   │   ├── middleware/
│   │   └── utils/
│   ├── package.json
│   └── server.js
├── docker-compose.yml
└── README.md
```

## Step-by-Step Implementation

### 1.1 Django Backend Setup

#### Step 1: Initialize Django Project
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install Django and dependencies
pip install django djangorestframework django-cors-headers
pip install psycopg2-binary python-decouple djangorestframework-simplejwt
pip install redis celery django-redis
pip install pydantic pydantic-settings

# Create Django project
django-admin startproject chatapp backend
cd backend
python manage.py startapp authentication
python manage.py startapp users
python manage.py startapp core
```

#### Step 2: Configure Django Settings
```python
# backend/chatapp/settings.py
import os
from decouple import config
from datetime import timedelta

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework_simplejwt',
    'corsheaders',
    'authentication',
    'users',
    'core',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Database Configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': config('DB_NAME', default='chatapp'),
        'USER': config('DB_USER', default='postgres'),
        'PASSWORD': config('DB_PASSWORD', default='password'),
        'HOST': config('DB_HOST', default='localhost'),
        'PORT': config('DB_PORT', default='5432'),
    }
}

# JWT Configuration
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
}

# REST Framework Configuration
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
}

# CORS Configuration
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # React development server
    "http://127.0.0.1:3000",
]

# Redis Configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

#### Step 3: Create User Model and Pydantic Schemas
```python
# backend/users/models.py
import uuid
from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=30)
    last_name = models.CharField(max_length=30)
    profile_picture = models.URLField(blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    last_seen = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']

    class Meta:
        db_table = 'users'

    def __str__(self):
        return self.email
```

```python
# backend/users/schemas.py
from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime
import uuid

class UserBase(BaseModel):
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=30)
    first_name: str = Field(..., min_length=1, max_length=30)
    last_name: str = Field(..., min_length=1, max_length=30)

class UserCreate(UserBase):
    password: str = Field(..., min_length=8)

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserResponse(UserBase):
    id: uuid.UUID
    profile_picture: Optional[str] = None
    is_verified: bool
    last_seen: datetime
    created_at: datetime

    class Config:
        from_attributes = True

class TokenResponse(BaseModel):
    access: str
    refresh: str

class AuthResponse(BaseModel):
    user: UserResponse
    tokens: TokenResponse
```

#### Step 4: Authentication Views with Pydantic Validation
```python
# backend/authentication/views.py
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password
from pydantic import ValidationError
from users.models import User
from users.schemas import UserCreate, UserLogin, UserResponse, AuthResponse, TokenResponse

@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    try:
        # Validate input with Pydantic
        user_data = UserCreate(**request.data)

        # Check if user already exists
        if User.objects.filter(email=user_data.email).exists():
            return Response({'error': 'User with this email already exists'},
                          status=status.HTTP_400_BAD_REQUEST)

        if User.objects.filter(username=user_data.username).exists():
            return Response({'error': 'User with this username already exists'},
                          status=status.HTTP_400_BAD_REQUEST)

        # Create user
        user = User.objects.create(
            email=user_data.email,
            username=user_data.username,
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            password=make_password(user_data.password)
        )

        # Generate tokens
        refresh = RefreshToken.for_user(user)

        # Prepare response using Pydantic schemas
        user_response = UserResponse.from_orm(user)
        tokens = TokenResponse(
            access=str(refresh.access_token),
            refresh=str(refresh)
        )
        auth_response = AuthResponse(user=user_response, tokens=tokens)

        return Response(auth_response.dict(), status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    try:
        # Validate input with Pydantic
        login_data = UserLogin(**request.data)

        # Authenticate user
        user = authenticate(email=login_data.email, password=login_data.password)

        if user:
            # Generate tokens
            refresh = RefreshToken.for_user(user)

            # Prepare response using Pydantic schemas
            user_response = UserResponse.from_orm(user)
            tokens = TokenResponse(
                access=str(refresh.access_token),
                refresh=str(refresh)
            )
            auth_response = AuthResponse(user=user_response, tokens=tokens)

            return Response(auth_response.dict(), status=status.HTTP_200_OK)

        return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

### 1.2 React Frontend Setup

#### Step 1: Initialize React Project
```bash
# Create React app with TypeScript
npx create-react-app frontend --template typescript
cd frontend

# Install additional dependencies
npm install @reduxjs/toolkit react-redux
npm install axios socket.io-client
npm install @types/socket.io-client
npm install react-router-dom @types/react-router-dom
npm install @mui/material @emotion/react @emotion/styled
npm install @mui/icons-material
```

#### Step 2: Setup Authentication Context
```typescript
// frontend/src/contexts/AuthContext.tsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import axios from 'axios';

interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const authReducer = (state: AuthState, action: any): AuthState => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loading: false,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    default:
      return state;
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, {
    user: null,
    token: localStorage.getItem('token'),
    isAuthenticated: false,
    loading: true,
  });

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      // Verify token validity
      // Implementation depends on your token verification endpoint
    }
    dispatch({ type: 'SET_LOADING', payload: false });
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const response = await axios.post('/api/auth/login/', { email, password });
      const { user, tokens } = response.data;
      
      localStorage.setItem('token', tokens.access);
      localStorage.setItem('refreshToken', tokens.refresh);
      axios.defaults.headers.common['Authorization'] = `Bearer ${tokens.access}`;
      
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token: tokens.access } });
    } catch (error) {
      throw error;
    }
  };

  const register = async (userData: any) => {
    try {
      const response = await axios.post('/api/auth/register/', userData);
      const { user, tokens } = response.data;
      
      localStorage.setItem('token', tokens.access);
      localStorage.setItem('refreshToken', tokens.refresh);
      axios.defaults.headers.common['Authorization'] = `Bearer ${tokens.access}`;
      
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token: tokens.access } });
    } catch (error) {
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    delete axios.defaults.headers.common['Authorization'];
    dispatch({ type: 'LOGOUT' });
  };

  return (
    <AuthContext.Provider value={{ ...state, login, register, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### 1.3 Node.js Socket Server Setup

#### Step 1: Initialize Socket Server with Prisma and Zod
```bash
mkdir socket-server
cd socket-server
npm init -y

# Install dependencies
npm install socket.io express cors
npm install jsonwebtoken redis
npm install prisma @prisma/client zod
npm install @types/node typescript ts-node nodemon --save-dev

# Initialize Prisma
npx prisma init
```

#### Step 2: Prisma Schema Configuration
```prisma
// socket-server/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              String   @id @default(uuid()) @db.Uuid
  email           String   @unique
  username        String   @unique
  firstName       String   @map("first_name")
  lastName        String   @map("last_name")
  profilePicture  String?  @map("profile_picture")
  isVerified      Boolean  @default(false) @map("is_verified")
  lastSeen        DateTime @default(now()) @map("last_seen")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations for future phases
  sentMessages     Message[] @relation("MessageSender")
  conversations    ConversationParticipant[]

  @@map("users")
}

model Conversation {
  id          String   @id @default(uuid()) @db.Uuid
  name        String?
  type        ConversationType @default(DIRECT)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  participants ConversationParticipant[]
  messages     Message[]

  @@map("conversations")
}

model ConversationParticipant {
  id             String   @id @default(uuid()) @db.Uuid
  conversationId String   @map("conversation_id") @db.Uuid
  userId         String   @map("user_id") @db.Uuid
  joinedAt       DateTime @default(now()) @map("joined_at")
  role           ParticipantRole @default(MEMBER)

  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([conversationId, userId])
  @@map("conversation_participants")
}

model Message {
  id             String      @id @default(uuid()) @db.Uuid
  conversationId String      @map("conversation_id") @db.Uuid
  senderId       String      @map("sender_id") @db.Uuid
  content        String
  messageType    MessageType @default(TEXT) @map("message_type")
  createdAt      DateTime    @default(now()) @map("created_at")
  updatedAt      DateTime    @updatedAt @map("updated_at")

  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender       User         @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)

  @@map("messages")
}

enum ConversationType {
  DIRECT
  GROUP
}

enum ParticipantRole {
  ADMIN
  MEMBER
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  SYSTEM
}
```

#### Step 3: Zod Schemas for Validation
```typescript
// socket-server/src/schemas/index.ts
import { z } from 'zod';

// User schemas
export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  username: z.string().min(3).max(30),
  firstName: z.string().min(1).max(30),
  lastName: z.string().min(1).max(30),
  profilePicture: z.string().url().optional(),
  isVerified: z.boolean(),
  lastSeen: z.date(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const UserCreateSchema = z.object({
  email: z.string().email(),
  username: z.string().min(3).max(30),
  firstName: z.string().min(1).max(30),
  lastName: z.string().min(1).max(30),
  password: z.string().min(8),
});

// Message schemas
export const MessageSchema = z.object({
  id: z.string().uuid(),
  conversationId: z.string().uuid(),
  senderId: z.string().uuid(),
  content: z.string().min(1),
  messageType: z.enum(['TEXT', 'IMAGE', 'FILE', 'SYSTEM']),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const MessageCreateSchema = z.object({
  conversationId: z.string().uuid(),
  content: z.string().min(1).max(4000),
  messageType: z.enum(['TEXT', 'IMAGE', 'FILE']).default('TEXT'),
});

// Conversation schemas
export const ConversationSchema = z.object({
  id: z.string().uuid(),
  name: z.string().optional(),
  type: z.enum(['DIRECT', 'GROUP']),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const ConversationCreateSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  type: z.enum(['DIRECT', 'GROUP']).default('DIRECT'),
  participantIds: z.array(z.string().uuid()).min(1),
});

// Socket event schemas
export const SocketAuthSchema = z.object({
  token: z.string(),
});

export const JoinRoomSchema = z.object({
  conversationId: z.string().uuid(),
});

export const TypingEventSchema = z.object({
  conversationId: z.string().uuid(),
  isTyping: z.boolean(),
});

// Response schemas
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.string().optional(),
  timestamp: z.date().default(() => new Date()),
});

export type User = z.infer<typeof UserSchema>;
export type UserCreate = z.infer<typeof UserCreateSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type MessageCreate = z.infer<typeof MessageCreateSchema>;
export type Conversation = z.infer<typeof ConversationSchema>;
export type ConversationCreate = z.infer<typeof ConversationCreateSchema>;
export type SocketAuth = z.infer<typeof SocketAuthSchema>;
export type JoinRoom = z.infer<typeof JoinRoomSchema>;
export type TypingEvent = z.infer<typeof TypingEventSchema>;
export type ApiResponse = z.infer<typeof ApiResponseSchema>;
```

#### Step 4: Socket Server with Prisma and Zod Integration
```typescript
// socket-server/src/server.ts
import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { createClient } from 'redis';
import { z } from 'zod';
import {
  SocketAuthSchema,
  JoinRoomSchema,
  MessageCreateSchema,
  TypingEventSchema,
  ApiResponseSchema
} from './schemas';

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const prisma = new PrismaClient();
const redisClient = createClient();

// JWT Authentication middleware for socket connections
io.use(async (socket, next) => {
  try {
    const authData = SocketAuthSchema.parse(socket.handshake.auth);

    const decoded = jwt.verify(authData.token, process.env.JWT_SECRET || 'your-secret-key') as any;

    // Verify user exists in database
    const user = await prisma.user.findUnique({
      where: { id: decoded.user_id }
    });

    if (!user) {
      return next(new Error('User not found'));
    }

    socket.userId = user.id;
    socket.user = user;
    next();
  } catch (err) {
    next(new Error('Authentication error'));
  }
});

io.on('connection', (socket) => {
  console.log(`User ${socket.userId} connected`);

  // Join user to their personal room
  socket.join(`user_${socket.userId}`);

  // Handle joining conversation rooms
  socket.on('join_conversation', async (data) => {
    try {
      const { conversationId } = JoinRoomSchema.parse(data);

      // Verify user is participant in conversation
      const participant = await prisma.conversationParticipant.findFirst({
        where: {
          conversationId,
          userId: socket.userId
        }
      });

      if (participant) {
        socket.join(`conversation_${conversationId}`);
        socket.emit('joined_conversation', { conversationId });
      } else {
        socket.emit('error', { message: 'Not authorized to join this conversation' });
      }
    } catch (error) {
      socket.emit('error', { message: 'Invalid conversation data' });
    }
  });

  // Handle typing indicators
  socket.on('typing', (data) => {
    try {
      const typingData = TypingEventSchema.parse(data);
      socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
        userId: socket.userId,
        conversationId: typingData.conversationId,
        isTyping: typingData.isTyping
      });
    } catch (error) {
      socket.emit('error', { message: 'Invalid typing data' });
    }
  });

  // Handle user going online
  socket.on('user_online', async () => {
    await prisma.user.update({
      where: { id: socket.userId },
      data: { lastSeen: new Date() }
    });

    socket.broadcast.emit('user_status_change', {
      userId: socket.userId,
      status: 'online'
    });
  });

  // Handle disconnection
  socket.on('disconnect', async () => {
    console.log(`User ${socket.userId} disconnected`);

    await prisma.user.update({
      where: { id: socket.userId },
      data: { lastSeen: new Date() }
    });

    socket.broadcast.emit('user_status_change', {
      userId: socket.userId,
      status: 'offline'
    });
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Socket server running on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  await redisClient.quit();
  process.exit(0);
});
```

## Integration Points

### Django ↔ Socket Server
- Shared PostgreSQL database via Prisma ORM
- Shared JWT secret for authentication
- Redis for session management
- Consistent data schemas via Pydantic and Zod

### Frontend ↔ Backend
- Axios interceptors for token refresh
- Error handling for API calls
- Protected routes implementation
- Type-safe API responses

## Shared Schema Definitions

### Step 5: Create Shared Schema Documentation
```typescript
// shared-schemas/README.md
# Shared Schema Definitions

This document outlines the shared data structures used across all services:
- Django Backend (Pydantic models)
- Node.js Socket Server (Zod schemas)
- React Frontend (TypeScript interfaces)

## User Entity
```json
{
  "id": "uuid",
  "email": "string (email format)",
  "username": "string (3-30 chars)",
  "firstName": "string (1-30 chars)",
  "lastName": "string (1-30 chars)",
  "profilePicture": "string (url, optional)",
  "isVerified": "boolean",
  "lastSeen": "datetime",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

## Message Entity
```json
{
  "id": "uuid",
  "conversationId": "uuid",
  "senderId": "uuid",
  "content": "string (1-4000 chars)",
  "messageType": "enum: TEXT|IMAGE|FILE|SYSTEM",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

## Conversation Entity
```json
{
  "id": "uuid",
  "name": "string (optional, 1-100 chars)",
  "type": "enum: DIRECT|GROUP",
  "createdAt": "datetime",
  "updatedAt": "datetime"
}
```

## API Response Format
```json
{
  "success": "boolean",
  "data": "any (optional)",
  "error": "string (optional)",
  "timestamp": "datetime"
}
```

## Environment Configuration

### Step 6: Environment Setup
```bash
# socket-server/.env
DATABASE_URL="postgresql://postgres:password@localhost:5432/chatapp"
JWT_SECRET="your-jwt-secret-key"
REDIS_URL="redis://localhost:6379"
PORT=3001
NODE_ENV="development"
```

```bash
# backend/.env
DB_NAME=chatapp
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432
JWT_SECRET=your-jwt-secret-key
REDIS_URL=redis://localhost:6379
DEBUG=True
```

## Acceptance Criteria

### Phase 1 Completion Checklist
- [ ] Django project setup with PostgreSQL connection
- [ ] Pydantic schemas implemented for data validation
- [ ] User registration and login API endpoints working
- [ ] JWT authentication implemented
- [ ] React frontend with authentication flow
- [ ] Socket.io server with JWT authentication
- [ ] Prisma ORM configured and connected to PostgreSQL
- [ ] Zod schemas implemented for real-time validation
- [ ] Redis integration for session management
- [ ] CORS configuration working
- [ ] Shared schema definitions documented
- [ ] Basic error handling implemented

### Testing Requirements
- [ ] Unit tests for authentication views
- [ ] Pydantic schema validation tests
- [ ] Integration tests for login/register flow
- [ ] Socket connection authentication tests
- [ ] Prisma database connection tests
- [ ] Zod schema validation tests
- [ ] Frontend authentication component tests

## Common Issues & Troubleshooting

### Database Connection Issues
```bash
# Check PostgreSQL service
sudo systemctl status postgresql

# Create database
sudo -u postgres createdb chatapp
sudo -u postgres createuser chatapp_user
```

### CORS Issues
- Ensure frontend URL is in CORS_ALLOWED_ORIGINS
- Check socket.io CORS configuration
- Verify API endpoints are accessible

### JWT Token Issues
- Ensure JWT_SECRET is consistent across services
- Check token expiration times
- Implement token refresh logic

### Socket Connection Issues
- Verify JWT token is passed in handshake
- Check Redis connection
- Ensure proper error handling

### Prisma Issues
```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma db push

# Reset database if needed
npx prisma db reset
```

### Schema Validation Issues
- Ensure Pydantic and Zod schemas are synchronized
- Check for proper error handling in validation
- Verify consistent field naming across services

## Next Phase Dependencies
- User authentication system must be fully functional
- Database models and migrations completed
- Socket server authentication working
- Prisma ORM connected and functional
- Pydantic and Zod validation working
- Shared schema definitions established
- Frontend authentication context established

This foundation is critical for all subsequent phases. Ensure all tests pass and schema validation is working correctly before proceeding to Phase 2.
