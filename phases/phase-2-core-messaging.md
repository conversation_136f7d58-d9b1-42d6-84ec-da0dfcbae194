# Phase 2: Core Messaging Infrastructure

**Duration**: 3-4 weeks | **Priority**: Critical

## Overview
This phase implements the core messaging functionality including database schema, real-time message delivery, conversation management, and basic messaging UI components.

## Prerequisites
- Phase 1 completed successfully
- User authentication system working
- Socket.io server with authentication
- PostgreSQL database configured
- Redis for caching

## Database Schema Implementation

### Step 1: Create Messaging Models

```python
# backend/apps/messaging/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Conversation(models.Model):
    CONVERSATION_TYPES = [
        ('direct', 'Direct Message'),
        ('group', 'Group Chat'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(max_length=10, choices=CONVERSATION_TYPES, default='direct')
    name = models.CharField(max_length=100, blank=True, null=True)  # For group chats
    description = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_conversations')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_message_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'conversations'
        ordering = ['-last_message_at']
    
    def __str__(self):
        if self.type == 'group':
            return self.name or f"Group {self.id}"
        return f"Direct conversation {self.id}"

class ConversationParticipant(models.Model):
    PARTICIPANT_ROLES = [
        ('admin', 'Admin'),
        ('member', 'Member'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_memberships')
    role = models.CharField(max_length=10, choices=PARTICIPANT_ROLES, default='member')
    joined_at = models.DateTimeField(auto_now_add=True)
    last_read_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'conversation_participants'
        unique_together = ['conversation', 'user']
    
    def __str__(self):
        return f"{self.user.username} in {self.conversation}"

class Message(models.Model):
    MESSAGE_TYPES = [
        ('text', 'Text Message'),
        ('image', 'Image'),
        ('file', 'File'),
        ('system', 'System Message'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    content = models.TextField()  # Will be encrypted in Phase 3
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='text')
    reply_to = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='replies')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_edited = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'messages'
        ordering = ['created_at']
    
    def __str__(self):
        return f"Message from {self.sender.username} in {self.conversation}"

class MessageStatus(models.Model):
    STATUS_CHOICES = [
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='statuses')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='message_statuses')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='sent')
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'message_statuses'
        unique_together = ['message', 'user']
```

### Step 2: Create and Run Migrations

```bash
# Create messaging app
cd backend
python manage.py startapp messaging

# Add to INSTALLED_APPS in settings.py
# Create migrations
python manage.py makemigrations messaging
python manage.py migrate
```

## API Implementation

### Step 3: Serializers

```python
# backend/apps/messaging/serializers.py
from rest_framework import serializers
from .models import Conversation, ConversationParticipant, Message, MessageStatus
from users.models import User

class UserBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'profile_picture']

class MessageSerializer(serializers.ModelSerializer):
    sender = UserBasicSerializer(read_only=True)
    reply_to = serializers.SerializerMethodField()
    
    class Meta:
        model = Message
        fields = ['id', 'content', 'message_type', 'sender', 'reply_to', 
                 'created_at', 'updated_at', 'is_edited', 'is_deleted']
    
    def get_reply_to(self, obj):
        if obj.reply_to:
            return {
                'id': str(obj.reply_to.id),
                'content': obj.reply_to.content[:100],  # Truncated preview
                'sender': obj.reply_to.sender.username
            }
        return None

class ConversationParticipantSerializer(serializers.ModelSerializer):
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = ConversationParticipant
        fields = ['id', 'user', 'role', 'joined_at', 'last_read_at']

class ConversationSerializer(serializers.ModelSerializer):
    participants = ConversationParticipantSerializer(many=True, read_only=True)
    last_message = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = ['id', 'type', 'name', 'description', 'participants', 
                 'last_message', 'unread_count', 'created_at', 'last_message_at']
    
    def get_last_message(self, obj):
        last_message = obj.messages.filter(is_deleted=False).last()
        if last_message:
            return MessageSerializer(last_message).data
        return None
    
    def get_unread_count(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            participant = obj.participants.filter(user=request.user).first()
            if participant:
                return obj.messages.filter(
                    created_at__gt=participant.last_read_at,
                    is_deleted=False
                ).exclude(sender=request.user).count()
        return 0

class CreateConversationSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=['direct', 'group'])
    name = serializers.CharField(max_length=100, required=False, allow_blank=True)
    description = serializers.CharField(required=False, allow_blank=True)
    participant_ids = serializers.ListField(
        child=serializers.UUIDField(),
        min_length=1
    )
    
    def validate_participant_ids(self, value):
        # Ensure all participant IDs exist
        existing_users = User.objects.filter(id__in=value).count()
        if existing_users != len(value):
            raise serializers.ValidationError("One or more participant IDs are invalid")
        return value

class SendMessageSerializer(serializers.ModelSerializer):
    reply_to_id = serializers.UUIDField(required=False, allow_null=True)
    
    class Meta:
        model = Message
        fields = ['content', 'message_type', 'reply_to_id']
    
    def validate_reply_to_id(self, value):
        if value:
            conversation_id = self.context.get('conversation_id')
            if not Message.objects.filter(id=value, conversation_id=conversation_id).exists():
                raise serializers.ValidationError("Reply message not found in this conversation")
        return value
```

### Step 4: API Views

```python
# backend/apps/messaging/views.py
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from django.db.models import Q, Max
from django.utils import timezone
from .models import Conversation, ConversationParticipant, Message
from .serializers import (
    ConversationSerializer, MessageSerializer, 
    CreateConversationSerializer, SendMessageSerializer
)

class MessagePagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100

class ConversationListView(generics.ListAPIView):
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return Conversation.objects.filter(
            participants__user=self.request.user,
            participants__is_active=True
        ).distinct().order_by('-last_message_at')

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_conversation(request):
    serializer = CreateConversationSerializer(data=request.data)
    if serializer.is_valid():
        data = serializer.validated_data
        
        # For direct messages, check if conversation already exists
        if data['type'] == 'direct' and len(data['participant_ids']) == 1:
            other_user_id = data['participant_ids'][0]
            existing_conversation = Conversation.objects.filter(
                type='direct',
                participants__user=request.user
            ).filter(
                participants__user_id=other_user_id
            ).first()
            
            if existing_conversation:
                return Response(
                    ConversationSerializer(existing_conversation, context={'request': request}).data,
                    status=status.HTTP_200_OK
                )
        
        # Create new conversation
        conversation = Conversation.objects.create(
            type=data['type'],
            name=data.get('name', ''),
            description=data.get('description', ''),
            created_by=request.user
        )
        
        # Add creator as participant
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=request.user,
            role='admin' if data['type'] == 'group' else 'member'
        )
        
        # Add other participants
        for user_id in data['participant_ids']:
            if user_id != request.user.id:
                ConversationParticipant.objects.create(
                    conversation=conversation,
                    user_id=user_id,
                    role='member'
                )
        
        return Response(
            ConversationSerializer(conversation, context={'request': request}).data,
            status=status.HTTP_201_CREATED
        )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ConversationMessagesView(generics.ListAPIView):
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = MessagePagination
    
    def get_queryset(self):
        conversation_id = self.kwargs['conversation_id']
        conversation = get_object_or_404(Conversation, id=conversation_id)
        
        # Check if user is participant
        if not conversation.participants.filter(user=self.request.user, is_active=True).exists():
            return Message.objects.none()
        
        return conversation.messages.filter(is_deleted=False).order_by('-created_at')

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def send_message(request, conversation_id):
    conversation = get_object_or_404(Conversation, id=conversation_id)
    
    # Check if user is participant
    participant = conversation.participants.filter(user=request.user, is_active=True).first()
    if not participant:
        return Response({'error': 'Not a participant in this conversation'}, 
                       status=status.HTTP_403_FORBIDDEN)
    
    serializer = SendMessageSerializer(
        data=request.data, 
        context={'conversation_id': conversation_id}
    )
    
    if serializer.is_valid():
        message = serializer.save(
            conversation=conversation,
            sender=request.user,
            reply_to_id=serializer.validated_data.get('reply_to_id')
        )
        
        # Update conversation last_message_at
        conversation.last_message_at = timezone.now()
        conversation.save(update_fields=['last_message_at'])
        
        # Emit socket event (will be implemented in socket handlers)
        from .socket_handlers import emit_new_message
        emit_new_message(message)
        
        return Response(
            MessageSerializer(message).data,
            status=status.HTTP_201_CREATED
        )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def mark_messages_read(request, conversation_id):
    conversation = get_object_or_404(Conversation, id=conversation_id)
    participant = conversation.participants.filter(user=request.user, is_active=True).first()
    
    if not participant:
        return Response({'error': 'Not a participant in this conversation'}, 
                       status=status.HTTP_403_FORBIDDEN)
    
    # Update last_read_at
    participant.last_read_at = timezone.now()
    participant.save(update_fields=['last_read_at'])
    
    return Response({'status': 'Messages marked as read'})
```

## Socket.io Integration

### Step 5: Socket Event Handlers

```javascript
// socket-server/src/handlers/messageHandlers.js
const jwt = require('jsonwebtoken');

class MessageHandler {
  constructor(io, redisClient) {
    this.io = io;
    this.redisClient = redisClient;
  }

  handleConnection(socket) {
    // Join user to their conversations
    socket.on('join_conversations', async (conversationIds) => {
      try {
        // Verify user has access to these conversations
        for (const conversationId of conversationIds) {
          const hasAccess = await this.verifyConversationAccess(socket.userId, conversationId);
          if (hasAccess) {
            socket.join(`conversation_${conversationId}`);
          }
        }
        socket.emit('conversations_joined', { success: true });
      } catch (error) {
        socket.emit('error', { message: 'Failed to join conversations' });
      }
    });

    // Handle new message
    socket.on('send_message', async (data) => {
      try {
        const { conversationId, content, messageType, replyToId } = data;
        
        // Verify user has access to conversation
        const hasAccess = await this.verifyConversationAccess(socket.userId, conversationId);
        if (!hasAccess) {
          socket.emit('error', { message: 'Access denied' });
          return;
        }

        // Emit to all participants in the conversation
        socket.to(`conversation_${conversationId}`).emit('new_message', {
          id: data.tempId, // Temporary ID for optimistic updates
          conversationId,
          senderId: socket.userId,
          content,
          messageType,
          replyToId,
          timestamp: new Date().toISOString(),
          status: 'sent'
        });

        // Acknowledge to sender
        socket.emit('message_sent', {
          tempId: data.tempId,
          status: 'delivered'
        });

      } catch (error) {
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data) => {
      socket.to(`conversation_${data.conversationId}`).emit('user_typing', {
        userId: socket.userId,
        conversationId: data.conversationId,
        isTyping: true
      });
    });

    socket.on('typing_stop', (data) => {
      socket.to(`conversation_${data.conversationId}`).emit('user_typing', {
        userId: socket.userId,
        conversationId: data.conversationId,
        isTyping: false
      });
    });

    // Handle message read receipts
    socket.on('mark_read', (data) => {
      socket.to(`conversation_${data.conversationId}`).emit('messages_read', {
        userId: socket.userId,
        conversationId: data.conversationId,
        lastReadMessageId: data.lastReadMessageId,
        timestamp: new Date().toISOString()
      });
    });
  }

  async verifyConversationAccess(userId, conversationId) {
    // This would typically query your database
    // For now, we'll assume access is granted
    // In a real implementation, you'd check the conversation_participants table
    return true;
  }

  // Method to emit new message from Django backend
  emitNewMessage(messageData) {
    this.io.to(`conversation_${messageData.conversationId}`).emit('new_message', messageData);
  }
}

module.exports = MessageHandler;
```

## Frontend Implementation

### Step 6: Message Components

```typescript
// frontend/src/components/Chat/MessageList.tsx
import React, { useEffect, useRef } from 'react';
import { Box, Typography, Avatar, Paper } from '@mui/material';
import { formatDistanceToNow } from 'date-fns';

interface Message {
  id: string;
  content: string;
  sender: {
    id: string;
    username: string;
    first_name: string;
    last_name: string;
    profile_picture?: string;
  };
  created_at: string;
  message_type: string;
  reply_to?: {
    id: string;
    content: string;
    sender: string;
  };
}

interface MessageListProps {
  messages: Message[];
  currentUserId: string;
}

const MessageList: React.FC<MessageListProps> = ({ messages, currentUserId }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
      {messages.map((message) => {
        const isOwnMessage = message.sender.id === currentUserId;
        
        return (
          <Box
            key={message.id}
            sx={{
              display: 'flex',
              justifyContent: isOwnMessage ? 'flex-end' : 'flex-start',
              mb: 2,
            }}
          >
            <Box sx={{ display: 'flex', maxWidth: '70%', alignItems: 'flex-end' }}>
              {!isOwnMessage && (
                <Avatar
                  src={message.sender.profile_picture}
                  sx={{ width: 32, height: 32, mr: 1 }}
                >
                  {message.sender.first_name[0]}
                </Avatar>
              )}
              
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  backgroundColor: isOwnMessage ? 'primary.main' : 'grey.100',
                  color: isOwnMessage ? 'white' : 'text.primary',
                  borderRadius: 2,
                  borderBottomRightRadius: isOwnMessage ? 0 : 2,
                  borderBottomLeftRadius: isOwnMessage ? 2 : 0,
                }}
              >
                {message.reply_to && (
                  <Box
                    sx={{
                      borderLeft: 3,
                      borderColor: 'divider',
                      pl: 1,
                      mb: 1,
                      opacity: 0.7,
                    }}
                  >
                    <Typography variant="caption" display="block">
                      {message.reply_to.sender}
                    </Typography>
                    <Typography variant="body2">
                      {message.reply_to.content}
                    </Typography>
                  </Box>
                )}
                
                <Typography variant="body1">{message.content}</Typography>
                
                <Typography
                  variant="caption"
                  sx={{
                    display: 'block',
                    mt: 0.5,
                    opacity: 0.7,
                  }}
                >
                  {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                </Typography>
              </Paper>
            </Box>
          </Box>
        );
      })}
      <div ref={messagesEndRef} />
    </Box>
  );
};

export default MessageList;
```

### Step 7: Message Input Component

```typescript
// frontend/src/components/Chat/MessageInput.tsx
import React, { useState, useRef } from 'react';
import { Box, TextField, IconButton, Paper } from '@mui/material';
import { Send as SendIcon, AttachFile as AttachFileIcon } from '@mui/icons-material';

interface MessageInputProps {
  onSendMessage: (content: string, messageType?: string) => void;
  onTypingStart: () => void;
  onTypingStop: () => void;
  disabled?: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  onTypingStart,
  onTypingStop,
  disabled = false,
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setMessage(value);

    // Handle typing indicators
    if (value && !isTyping) {
      setIsTyping(true);
      onTypingStart();
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      onTypingStop();
    }, 1000);
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
      
      // Stop typing indicator
      if (isTyping) {
        setIsTyping(false);
        onTypingStop();
      }
      
      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit(event);
    }
  };

  return (
    <Paper elevation={1} sx={{ p: 2 }}>
      <Box component="form" onSubmit={handleSubmit} sx={{ display: 'flex', gap: 1 }}>
        <IconButton color="primary" disabled={disabled}>
          <AttachFileIcon />
        </IconButton>
        
        <TextField
          fullWidth
          multiline
          maxRows={4}
          placeholder="Type a message..."
          value={message}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          disabled={disabled}
          variant="outlined"
          size="small"
        />
        
        <IconButton
          type="submit"
          color="primary"
          disabled={!message.trim() || disabled}
        >
          <SendIcon />
        </IconButton>
      </Box>
    </Paper>
  );
};

export default MessageInput;
```

## Integration Points

### Django ↔ Socket Server
- Message creation triggers socket emission
- Shared Redis for real-time data
- Database queries for message history

### Frontend ↔ Backend
- REST API for message history
- Socket.io for real-time updates
- Optimistic UI updates

## Acceptance Criteria

### Phase 2 Completion Checklist
- [ ] Database models created and migrated
- [ ] Conversation creation API working
- [ ] Message sending/receiving API functional
- [ ] Real-time message delivery via Socket.io
- [ ] Message history pagination implemented
- [ ] Typing indicators working
- [ ] Read receipts functional
- [ ] Basic message UI components complete

### Testing Requirements
- [ ] Unit tests for models and serializers
- [ ] API endpoint integration tests
- [ ] Socket event handling tests
- [ ] Frontend component tests
- [ ] Real-time messaging flow tests

## Common Issues & Troubleshooting

### Message Ordering Issues
- Ensure proper timestamp handling
- Use database-generated timestamps
- Handle client-server time differences

### Socket Connection Problems
- Verify JWT token in socket handshake
- Check room joining/leaving logic
- Monitor connection state

### Performance Issues
- Implement message pagination
- Use database indexes on frequently queried fields
- Consider message caching strategies

## Next Phase Dependencies
- Core messaging must be fully functional
- Real-time delivery working reliably
- Message persistence confirmed
- UI components responsive and tested

### URL Configuration
```python
# backend/apps/messaging/urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('conversations/', views.ConversationListView.as_view(), name='conversation-list'),
    path('conversations/create/', views.create_conversation, name='create-conversation'),
    path('conversations/<uuid:conversation_id>/messages/', views.ConversationMessagesView.as_view(), name='conversation-messages'),
    path('conversations/<uuid:conversation_id>/send/', views.send_message, name='send-message'),
    path('conversations/<uuid:conversation_id>/read/', views.mark_messages_read, name='mark-read'),
]

# backend/chatapp/urls.py - Add to main urlpatterns
path('api/messaging/', include('messaging.urls')),
```

### Socket Server Integration
```javascript
// socket-server/src/server.js - Updated with message handlers
const MessageHandler = require('./handlers/messageHandlers');

const messageHandler = new MessageHandler(io, redisClient);

io.on('connection', (socket) => {
  console.log(`User ${socket.userId} connected`);

  // Initialize message handlers
  messageHandler.handleConnection(socket);

  // Existing connection logic...
});

// Export for Django integration
module.exports = { messageHandler };
```

This phase establishes the foundation for all messaging features. Ensure thorough testing before proceeding to Phase 3 (Encryption).
