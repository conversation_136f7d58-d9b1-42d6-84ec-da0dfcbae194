# Phase 1: Foundation & Infrastructure

**Duration**: 2-3 weeks | **Priority**: Critical

## Overview
This phase establishes the foundational architecture for the chat application, including project setup, user authentication, and basic infrastructure components.

## Prerequisites
- Node.js 18+ installed
- Python 3.9+ installed
- PostgreSQL 13+ installed
- Redis 6+ installed
- Git version control

## Architecture Setup

### Project Structure
```
chatapp/
├── backend/                 # Django + DRF
│   ├── chatapp/
│   ├── apps/
│   │   ├── authentication/
│   │   ├── users/
│   │   └── core/
│   ├── requirements.txt
│   └── manage.py
├── frontend/               # React.js
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── types/
│   ├── package.json
│   └── public/
├── socket-server/          # Node.js + Socket.io
│   ├── src/
│   │   ├── handlers/
│   │   ├── middleware/
│   │   └── utils/
│   ├── package.json
│   └── server.js
├── docker-compose.yml
└── README.md
```

## Step-by-Step Implementation

### 1.1 Django Backend Setup

#### Step 1: Initialize Django Project
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install Django and dependencies
pip install django djangorestframework django-cors-headers
pip install psycopg2-binary python-decouple djangorestframework-simplejwt
pip install redis celery django-redis

# Create Django project
django-admin startproject chatapp backend
cd backend
python manage.py startapp authentication
python manage.py startapp users
python manage.py startapp core
```

#### Step 2: Configure Django Settings
```python
# backend/chatapp/settings.py
import os
from decouple import config
from datetime import timedelta

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework_simplejwt',
    'corsheaders',
    'authentication',
    'users',
    'core',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Database Configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': config('DB_NAME', default='chatapp'),
        'USER': config('DB_USER', default='postgres'),
        'PASSWORD': config('DB_PASSWORD', default='password'),
        'HOST': config('DB_HOST', default='localhost'),
        'PORT': config('DB_PORT', default='5432'),
    }
}

# JWT Configuration
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=60),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
}

# REST Framework Configuration
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
}

# CORS Configuration
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # React development server
    "http://127.0.0.1:3000",
]

# Redis Configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

#### Step 3: Create User Model
```python
# backend/users/models.py
import uuid
from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=30)
    last_name = models.CharField(max_length=30)
    profile_picture = models.URLField(blank=True, null=True)
    is_verified = models.BooleanField(default=False)
    last_seen = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']

    class Meta:
        db_table = 'users'

    def __str__(self):
        return self.email
```

#### Step 4: Authentication Views
```python
# backend/authentication/views.py
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from users.models import User
from .serializers import UserRegistrationSerializer, UserLoginSerializer

@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    serializer = UserRegistrationSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.save()
        refresh = RefreshToken.for_user(user)
        return Response({
            'user': {
                'id': str(user.id),
                'email': user.email,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
            },
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }
        }, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        email = serializer.validated_data['email']
        password = serializer.validated_data['password']
        user = authenticate(email=email, password=password)
        
        if user:
            refresh = RefreshToken.for_user(user)
            return Response({
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'username': user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                },
                'tokens': {
                    'refresh': str(refresh),
                    'access': str(refresh.access_token),
                }
            })
        return Response({'error': 'Invalid credentials'}, status=status.HTTP_401_UNAUTHORIZED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
```

### 1.2 React Frontend Setup

#### Step 1: Initialize React Project
```bash
# Create React app with TypeScript
npx create-react-app frontend --template typescript
cd frontend

# Install additional dependencies
npm install @reduxjs/toolkit react-redux
npm install axios socket.io-client
npm install @types/socket.io-client
npm install react-router-dom @types/react-router-dom
npm install @mui/material @emotion/react @emotion/styled
npm install @mui/icons-material
```

#### Step 2: Setup Authentication Context
```typescript
// frontend/src/contexts/AuthContext.tsx
import React, { createContext, useContext, useReducer, useEffect } from 'react';
import axios from 'axios';

interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (userData: any) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const authReducer = (state: AuthState, action: any): AuthState => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loading: false,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    default:
      return state;
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, {
    user: null,
    token: localStorage.getItem('token'),
    isAuthenticated: false,
    loading: true,
  });

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      // Verify token validity
      // Implementation depends on your token verification endpoint
    }
    dispatch({ type: 'SET_LOADING', payload: false });
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const response = await axios.post('/api/auth/login/', { email, password });
      const { user, tokens } = response.data;
      
      localStorage.setItem('token', tokens.access);
      localStorage.setItem('refreshToken', tokens.refresh);
      axios.defaults.headers.common['Authorization'] = `Bearer ${tokens.access}`;
      
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token: tokens.access } });
    } catch (error) {
      throw error;
    }
  };

  const register = async (userData: any) => {
    try {
      const response = await axios.post('/api/auth/register/', userData);
      const { user, tokens } = response.data;
      
      localStorage.setItem('token', tokens.access);
      localStorage.setItem('refreshToken', tokens.refresh);
      axios.defaults.headers.common['Authorization'] = `Bearer ${tokens.access}`;
      
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user, token: tokens.access } });
    } catch (error) {
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    delete axios.defaults.headers.common['Authorization'];
    dispatch({ type: 'LOGOUT' });
  };

  return (
    <AuthContext.Provider value={{ ...state, login, register, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### 1.3 Node.js Socket Server Setup

#### Step 1: Initialize Socket Server
```bash
mkdir socket-server
cd socket-server
npm init -y

# Install dependencies
npm install socket.io express cors
npm install jsonwebtoken redis
npm install @types/node typescript ts-node nodemon --save-dev
```

#### Step 2: Basic Socket Server
```javascript
// socket-server/src/server.js
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const jwt = require('jsonwebtoken');
const redis = require('redis');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const redisClient = redis.createClient();

// JWT Authentication middleware for socket connections
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  
  if (!token) {
    return next(new Error('Authentication error'));
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    socket.userId = decoded.user_id;
    next();
  } catch (err) {
    next(new Error('Authentication error'));
  }
});

io.on('connection', (socket) => {
  console.log(`User ${socket.userId} connected`);

  // Join user to their personal room
  socket.join(`user_${socket.userId}`);

  // Handle user going online
  socket.on('user_online', () => {
    socket.broadcast.emit('user_status_change', {
      userId: socket.userId,
      status: 'online'
    });
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log(`User ${socket.userId} disconnected`);
    socket.broadcast.emit('user_status_change', {
      userId: socket.userId,
      status: 'offline'
    });
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Socket server running on port ${PORT}`);
});
```

## Integration Points

### Django ↔ Socket Server
- Shared JWT secret for authentication
- Redis for session management
- Database connection from both services

### Frontend ↔ Backend
- Axios interceptors for token refresh
- Error handling for API calls
- Protected routes implementation

## Acceptance Criteria

### Phase 1 Completion Checklist
- [ ] Django project setup with PostgreSQL connection
- [ ] User registration and login API endpoints working
- [ ] JWT authentication implemented
- [ ] React frontend with authentication flow
- [ ] Socket.io server with JWT authentication
- [ ] Redis integration for session management
- [ ] CORS configuration working
- [ ] Basic error handling implemented

### Testing Requirements
- [ ] Unit tests for authentication views
- [ ] Integration tests for login/register flow
- [ ] Socket connection authentication tests
- [ ] Frontend authentication component tests

## Common Issues & Troubleshooting

### Database Connection Issues
```bash
# Check PostgreSQL service
sudo systemctl status postgresql

# Create database
sudo -u postgres createdb chatapp
sudo -u postgres createuser chatapp_user
```

### CORS Issues
- Ensure frontend URL is in CORS_ALLOWED_ORIGINS
- Check socket.io CORS configuration
- Verify API endpoints are accessible

### JWT Token Issues
- Ensure JWT_SECRET is consistent across services
- Check token expiration times
- Implement token refresh logic

### Socket Connection Issues
- Verify JWT token is passed in handshake
- Check Redis connection
- Ensure proper error handling

## Next Phase Dependencies
- User authentication system must be fully functional
- Database models and migrations completed
- Socket server authentication working
- Frontend authentication context established

This foundation is critical for all subsequent phases. Ensure all tests pass before proceeding to Phase 2.
